<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format" xmlns:xlink="http://www.w3.org/1999/xlink">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body margin-top="10mm" margin-bottom="10mm" margin-left="10mm" margin-right="10mm"/>
        </fo:simple-page-master>
    </fo:layout-master-set>

    <!--    PAGE 1-->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">

            <!-- All About Me -->
            <fo:block font-size="16pt" font-family="Times New Roman"
                      text-align="center" font-weight="bold" space-after="3mm" margin-left="-10mm">
                Student Holistic Progress Report
            </fo:block>

            <fo:block font-size="14pt"
                      font-family="Times New Roman"
                      text-align="center"
                      font-weight="bold"
                      space-after="2mm"
                      border-bottom="1pt solid black"
                      padding-bottom="1mm">
                All About Me
            </fo:block>


            <fo:block space-before="2mm" space-after="2mm" font-size="10pt" font-family="Times New Roman">
                <fo:table>
                    <fo:table-column column-width="35mm"/>
                    <fo:table-column column-width="60mm"/>
                    <fo:table-column column-width="5mm"/>
                    <fo:table-column column-width="35mm"/>
                    <fo:table-column column-width="50mm"/>
                    <fo:table-body >
                        <fo:table-row >
                            <fo:table-cell font-weight="bold">
                                <fo:block >Student Name:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.studentName}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell font-weight="bold">
                                <fo:block >Grade:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.gradeName}"> </fo:block>
                            </fo:table-cell>
                        </fo:table-row>

                        <fo:table-row >
                            <fo:table-cell font-weight="bold">
                                <fo:block >Admission Number:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.admissionNo}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell font-weight="bold">
                                <fo:block>Section:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.sectionName}"> </fo:block>
                            </fo:table-cell>
                        </fo:table-row>

                        <fo:table-row >
                            <fo:table-cell font-weight="bold">
                                <fo:block >Fathers Name:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.fatherName}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell font-weight="bold">
                                <fo:block>Mothers Name:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.motherName}"> </fo:block>
                            </fo:table-cell>
                        </fo:table-row>

                        <fo:table-row >
                            <fo:table-cell font-weight="bold">
                                <fo:block >Father Number:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.fatherPhoneNo}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell font-weight="bold">
                                <fo:block >Mother Number:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.motherPhoneNo}"> </fo:block>
                            </fo:table-cell>
                        </fo:table-row>

                        <fo:table-row >
                            <fo:table-cell font-weight="bold">
                                <fo:block >Class Teacher Name:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.classTeacher}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell font-weight="bold">
                                <fo:block>Date of Birth:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.dob}"> </fo:block>
                            </fo:table-cell>
                        </fo:table-row>

                        <fo:table-row >
                            <fo:table-cell font-weight="bold">
                                <fo:block >Address:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.address}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell font-weight="bold">
                                <fo:block>House:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.house}"> </fo:block>
                            </fo:table-cell>
                        </fo:table-row>

                        <fo:table-row >
                            <fo:table-cell font-weight="bold">
                                <fo:block >Things I Like:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.thingsILike}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell font-weight="bold">
                                <fo:block>I Live In:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.iLiveIn}"> </fo:block>
                            </fo:table-cell>
                        </fo:table-row>

                        <fo:table-row >
                            <fo:table-cell font-weight="bold">
                                <fo:block >Favourite Colors:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.myFavouriteColoursAre}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell font-weight="bold">
                                <fo:block>Favourite Foods:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.myFavouriteFoods}"> </fo:block>
                            </fo:table-cell>
                        </fo:table-row>

                        <fo:table-row >
                            <fo:table-cell font-weight="bold">
                                <fo:block >Favourite Games:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.myFavouriteGames}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell font-weight="bold">
                                <fo:block>Favourite Animals:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.myFavouriteAnimals}"> </fo:block>
                            </fo:table-cell>
                        </fo:table-row>

                        <fo:table-row >
                            <fo:table-cell font-weight="bold">
                                <fo:block >Flower:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.flower}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell font-weight="bold">
                                <fo:block>Friends:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.myFriendsAre}"> </fo:block>
                            </fo:table-cell>
                        </fo:table-row>

                        <fo:table-row >
                            <fo:table-cell font-weight="bold">
                                <fo:block >Favourite Subject:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.subject}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell font-weight="bold">
                                <fo:block>Aim:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.aim}"> </fo:block>
                            </fo:table-cell>
                        </fo:table-row>

                        <fo:table-row >
                            <fo:table-cell font-weight="bold">
                                <fo:block >Term1 Height:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.term1Height}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell font-weight="bold">
                                <fo:block >Term2 Height:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.term2Height}"> </fo:block>
                            </fo:table-cell>
                        </fo:table-row>

                        <fo:table-row >
                            <fo:table-cell font-weight="bold">
                                <fo:block >Term1 Weight:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.term1Weight}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell font-weight="bold">
                                <fo:block >Term2 Weight:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.term2Weight}"> </fo:block>
                            </fo:table-cell>
                        </fo:table-row>

                        <fo:table-row >
                            <fo:table-cell font-weight="bold">
                                <fo:block >Blood Group:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.bloodGroup}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell font-weight="bold">
                                <fo:block >Dental:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.dental}"> </fo:block>
                            </fo:table-cell>
                        </fo:table-row>

                        <fo:table-row >
                            <fo:table-cell font-weight="bold">
                                <fo:block >Left Eye Sight:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.eyesightL}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell font-weight="bold">
                                <fo:block >Right Eye Sight:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.eyesightR}"> </fo:block>
                            </fo:table-cell>
                        </fo:table-row>

                    </fo:table-body>
                </fo:table>
            </fo:block>


            <!-- Glimpse of myself -->
            <fo:block font-size="14pt"
                      font-family="Times New Roman"
                      text-align="center"
                      font-weight="bold"
                      space-before="3mm"
                      space-after="2mm"
                      border-bottom="1pt solid black"
                      padding-bottom="1mm">
                Glimpse of Myself
            </fo:block>


            <fo:table table-layout="fixed" width="100%">
                <!-- Three equal columns -->
                <fo:table-column column-width="33%"/>
                <fo:table-column column-width="33%"/>
                <fo:table-column column-width="33%"/>

                <fo:table-body>
                    <fo:table-row>

                        <!-- A Glimpse of Myself -->
                        <fo:table-cell margin="10mm" text-align="center">
                            <fo:block font-size="12pt" font-weight="bold" space-after="2mm" font-family="Times New Roman">
                                A Glimpse of Myself
                            </fo:block>

                            <fo:block-container width="45mm" height="45mm"
                                                border="2pt solid #d5e6e8"
                                                background-color="white"
                                                display-align="center"
                                                text-align="center"
                                                margin-left="auto"
                                                margin-right="auto"
                                                padding="2mm">
                                <fo:block text-align="center">
                                    <th:block th:if="${model.body.aGlimpseOfMySelf != null}">
                                        <fo:instream-foreign-object content-width="scale-to-fit" content-height="scale-to-fit">
                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                 xmlns:xlink="http://www.w3.org/1999/xlink"
                                                 width="100%" height="100%" viewBox="0 0 100 100"
                                                 preserveAspectRatio="xMidYMid meet">

                                                <image x="0" y="0" width="100%" height="100%"
                                                       preserveAspectRatio="xMidYMid meet"
                                                       th:xlink:href="@{${model.body.aGlimpseOfMySelf}}"/>
                                            </svg>
                                        </fo:instream-foreign-object>
                                    </th:block>
                                </fo:block>
                            </fo:block-container>
                        </fo:table-cell>

                        <!-- A Glimpse of My Family -->
                        <fo:table-cell margin="10mm" text-align="center">
                            <fo:block font-size="12pt" font-weight="bold" space-after="2mm" font-family="Times New Roman">
                                A Glimpse of Family
                            </fo:block>

                            <fo:block-container width="45mm" height="45mm"
                                                border="2pt solid #d5e6e8"
                                                background-color="white"
                                                display-align="center"
                                                text-align="center"
                                                margin-left="auto"
                                                margin-right="auto"
                                                padding="2mm">
                                <fo:block text-align="center">
                                    <th:block th:if="${model.body.aGlimpseOfMyFamily != null}">
                                        <fo:instream-foreign-object content-width="scale-to-fit" content-height="scale-to-fit">
                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                 xmlns:xlink="http://www.w3.org/1999/xlink"
                                                 width="100%" height="100%" viewBox="0 0 100 100"
                                                 preserveAspectRatio="xMidYMid meet">

                                                <image x="0" y="0" width="100%" height="100%"
                                                       preserveAspectRatio="xMidYMid meet"
                                                       th:xlink:href="@{${model.body.aGlimpseOfMyFamily}}"/>
                                            </svg>
                                        </fo:instream-foreign-object>
                                    </th:block>
                                </fo:block>
                            </fo:block-container>
                        </fo:table-cell>

                        <!-- Learner's Portfolio -->
                        <fo:table-cell margin="10mm" text-align="center">
                            <fo:block font-size="12pt" font-weight="bold" space-after="2mm" font-family="Times New Roman">
                                Learner’s Portfolio
                            </fo:block>

                            <fo:block-container width="45mm" height="45mm"
                                                border="2pt solid #d5e6e8"
                                                background-color="white"
                                                display-align="center"
                                                text-align="center"
                                                margin-left="auto"
                                                margin-right="auto"
                                                padding="2mm">
                                <fo:block text-align="center">
                                    <th:block th:if="${model.body.learnersPortFolio != null}">
                                        <fo:instream-foreign-object content-width="scale-to-fit" content-height="scale-to-fit">
                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                 xmlns:xlink="http://www.w3.org/1999/xlink"
                                                 width="100%" height="100%" viewBox="0 0 100 100"
                                                 preserveAspectRatio="xMidYMid meet">

                                                <image x="0" y="0" width="100%" height="100%"
                                                       preserveAspectRatio="xMidYMid meet"
                                                       th:xlink:href="@{${model.body.learnersPortFolio}}"/>
                                            </svg>
                                        </fo:instream-foreign-object>
                                    </th:block>
                                </fo:block>
                            </fo:block-container>
                        </fo:table-cell>

                    </fo:table-row>
                </fo:table-body>
            </fo:table>


            <!-- Competencies Section -->
            <fo:block font-size="14pt"
                      font-family="Times New Roman"
                      text-align="center"
                      font-weight="bold"
                      space-before="3mm"
                      space-after="2mm"
                      border-bottom="1pt solid black"
                      padding-bottom="1mm">
                Competencies
            </fo:block>

            <!-- If Competencies Exist -->
            <th:block th:if="${model.body.competenciesList != null and !model.body.competenciesList.isEmpty()}">
                <fo:block space-before="2mm" space-after="2mm" font-family="Times New Roman">

                    <!-- Loop Subjects -->
                    <th:block th:each="subject : ${model.body.competenciesList}">
                        <!-- Subject Heading -->
                        <fo:block font-size="12pt" font-weight="bold"
                                  space-before="4mm" space-after="2mm" text-decoration="underline">
                            <fo:inline th:text="${subject.subjectName}"/>
                        </fo:block>

                        <!-- Loop Skills -->
                        <th:block th:each="detail : ${subject.skills}">
                            <!-- Skill Heading -->
                            <fo:block font-size="11pt" font-weight="bold"
                                      space-before="2mm" space-after="1mm"
                                      margin-left="5mm">
                                <fo:inline th:text="${detail.skillName}"/>
                            </fo:block>

                            <!-- Competency Table -->
                            <fo:table table-layout="fixed" width="100%" margin-left="10mm">
                                <fo:table-column column-width="75mm"/>
                                <fo:table-column column-width="50mm"/>
                                <fo:table-column column-width="50mm"/>

                                <!-- Header Row -->
                                <fo:table-header>
                                    <fo:table-row font-weight="bold" background-color="#f2f2f2">
                                        <fo:table-cell border="0.5pt solid black" text-align="left" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" margin-left="10pt">
                                            <fo:block>Competency</fo:block>
                                        </fo:table-cell>
                                        <fo:table-cell border="0.5pt solid black" text-align="left" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" margin-left="10pt">
                                            <fo:block>Term 1</fo:block>
                                        </fo:table-cell>
                                        <fo:table-cell border="0.5pt solid black" text-align="left" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" margin-left="10pt">
                                            <fo:block>Term 2</fo:block>
                                        </fo:table-cell>
                                    </fo:table-row>
                                </fo:table-header>

                                <!-- Body Rows -->
                                <fo:table-body>
                                    <th:block th:each="competency : ${detail.competencyDetails}">
                                        <fo:table-row>
                                            <fo:table-cell border="0.5pt solid black" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" text-align="left" margin-left="10pt">
                                                <fo:block th:text="${competency.name}"/>
                                            </fo:table-cell>
                                            <fo:table-cell font-size="9pt" border="0.5pt solid black" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" text-align="left" margin-left="10pt">
                                                <fo:block th:text="${competency.term1}"/>
                                            </fo:table-cell>
                                            <fo:table-cell border="0.5pt solid black" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" text-align="left" margin-left="10pt">
                                                <fo:block font-size="9pt" th:text="${competency.term2}"/>
                                            </fo:table-cell>
                                        </fo:table-row>
                                    </th:block>
                                </fo:table-body>
                            </fo:table>
                        </th:block>
                    </th:block>
                </fo:block>
            </th:block>

            <!-- If Competencies are Empty -->
            <th:block th:if="${model.body.competenciesList == null or model.body.competenciesList.isEmpty()}">
                <fo:block font-size="12pt" text-align="center" space-before="3mm" font-family="Times New Roman">
                    No Competency Data Available
                </fo:block>
            </th:block>

            <!-- Self Assessment Section -->
            <fo:block font-size="14pt"
                      font-family="Times New Roman"
                      text-align="center"
                      font-weight="bold"
                      space-before="3mm"
                      space-after="2mm"
                      border-bottom="1pt solid black"
                      padding-bottom="1mm">
                Self Assessment
            </fo:block>

            <!-- Check if data exists -->
            <th:block th:if="${model.body.selfAssessments != null and #lists.size(model.body.selfAssessments) > 0}">
                <fo:block space-before="2mm" space-after="2mm" font-family="Times New Roman">
                    <fo:table table-layout="fixed" width="100%">
                        <fo:table-column column-width="50%"/>
                        <fo:table-column column-width="25%"/>
                        <fo:table-column column-width="25%"/>

                        <!-- Header Row -->
                        <fo:table-header>
                            <fo:table-row font-weight="bold" background-color="#f2f2f2">
                                <fo:table-cell border="0.5pt solid black" padding-left="-3pt" text-align="left" padding-top="3pt" padding-bottom="3pt" margin-left="10pt">
                                    <fo:block>Name</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="0.5pt solid black" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" text-align="left" margin-left="10pt">
                                    <fo:block>Term 1</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="0.5pt solid black" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" text-align="left" margin-left="10pt">
                                    <fo:block>Term 2</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>

                        <!-- Body Rows -->
                        <fo:table-body>
                            <th:block th:each="subject : ${model.body.selfAssessments}">
                                <fo:table-row>
                                    <fo:table-cell border="0.5pt solid black" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" text-align="left" margin-left="10pt">
                                        <fo:block th:text="${subject.name}"/>
                                    </fo:table-cell>
                                    <fo:table-cell font-size="9pt" border="0.5pt solid black" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" text-align="left" margin-left="10pt">
                                        <fo:block th:text="${subject.term1}"/>
                                    </fo:table-cell>
                                    <fo:table-cell font-size="9pt" border="0.5pt solid black" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" text-align="left" margin-left="10pt">
                                        <fo:block th:text="${subject.term2}"/>
                                    </fo:table-cell>
                                </fo:table-row>
                            </th:block>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
            </th:block>


            <!-- No Data Case -->
            <th:block th:if="${model.body.selfAssessments == null or #lists.isEmpty(model.body.selfAssessments)}">
                <fo:block font-size="12pt" font-family="Times New Roman"
                          text-align="center" space-before="3mm">
                    No self-assessment data available
                </fo:block>
            </th:block>


            <!-- Peer Assessment Section -->
            <fo:block font-size="14pt"
                      font-family="Times New Roman"
                      text-align="center"
                      font-weight="bold"
                      space-before="3mm"
                      space-after="2mm"
                      border-bottom="1pt solid black"
                      padding-bottom="1mm">
                Peer Assessment
            </fo:block>

            <!-- Check if data exists -->
            <th:block th:if="${model.body.peerAssessment != null and #lists.size(model.body.peerAssessment) > 0}">
                <fo:block space-before="2mm" space-after="2mm" font-family="Times New Roman">
                    <fo:table table-layout="fixed" width="100%">
                        <fo:table-column column-width="50%"/>
                        <fo:table-column column-width="25%mm"/>
                        <fo:table-column column-width="25%"/>

                        <!-- Header Row -->
                        <fo:table-header>
                            <fo:table-row font-weight="bold" background-color="#f2f2f2">
                                <fo:table-cell border="0.5pt solid black" padding-left="-3pt" text-align="left" padding-top="3pt" padding-bottom="3pt" margin-left="10pt">
                                    <fo:block>Name</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="0.5pt solid black" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" text-align="left" margin-left="10pt">
                                    <fo:block>Term 1</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="0.5pt solid black" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" text-align="left" margin-left="10pt">
                                    <fo:block>Term 2</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>

                        <!-- Body Rows -->
                        <fo:table-body>
                            <th:block th:each="subject : ${model.body.peerAssessment}">
                                <fo:table-row>
                                    <fo:table-cell border="0.5pt solid black" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" text-align="left" margin-left="10pt">
                                        <fo:block th:text="${subject.name}"/>
                                    </fo:table-cell>
                                    <fo:table-cell font-size="9pt" border="0.5pt solid black" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" text-align="left" margin-left="10pt">
                                        <fo:block th:text="${subject.term1}"/>
                                    </fo:table-cell>
                                    <fo:table-cell font-size="9pt" border="0.5pt solid black" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" text-align="left" margin-left="10pt">
                                        <fo:block th:text="${subject.term2}"/>
                                    </fo:table-cell>
                                </fo:table-row>
                            </th:block>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
            </th:block>


            <!-- No Data Case -->
            <th:block th:if="${model.body.peerAssessment == null or #lists.isEmpty(model.body.peerAssessment)}">
                <fo:block font-size="12pt" font-family="Times New Roman"
                          text-align="center" space-before="3mm">
                    No peer-assessment data available
                </fo:block>
            </th:block>


            <!-- Summary Sheet Section -->
            <fo:block font-size="14pt"
                      font-family="Times New Roman"
                      text-align="center"
                      font-weight="bold"
                      space-before="3mm"
                      space-after="2mm"
                      border-bottom="1pt solid black"
                      padding-bottom="1mm">
                Summary Sheet
            </fo:block>

            <!-- Check if data exists -->
            <th:block th:if="${model.body.summarySheet != null and #lists.size(model.body.summarySheet) > 0}">
                <fo:block space-before="2mm" space-after="2mm" font-family="Times New Roman">
                    <fo:table table-layout="fixed" width="100%">
                        <fo:table-column column-width="50%"/>
                        <fo:table-column column-width="25%mm"/>
                        <fo:table-column column-width="25%"/>

                        <!-- Header Row -->
                        <fo:table-header>
                            <fo:table-row font-weight="bold" background-color="#f2f2f2">
                                <fo:table-cell border="0.5pt solid black" padding-left="-3pt" text-align="left" padding-top="3pt" padding-bottom="3pt" margin-left="10pt">
                                    <fo:block>Name</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="0.5pt solid black" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" text-align="left" margin-left="10pt">
                                    <fo:block>Term 1</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="0.5pt solid black" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" text-align="left" margin-left="10pt">
                                    <fo:block>Term 2</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>

                        <!-- Body Rows -->
                        <fo:table-body>
                            <th:block th:each="subject : ${model.body.summarySheet}">
                                <fo:table-row>
                                    <fo:table-cell border="0.5pt solid black" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" text-align="left" margin-left="10pt">
                                        <fo:block th:text="${subject.name}"/>
                                    </fo:table-cell>
                                    <fo:table-cell font-size="9pt" border="0.5pt solid black" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" text-align="left" margin-left="10pt">
                                        <fo:block th:text="${subject.term1}"/>
                                    </fo:table-cell>
                                    <fo:table-cell font-size="9pt" border="0.5pt solid black" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" text-align="left" margin-left="10pt">
                                        <fo:block th:text="${subject.term2}"/>
                                    </fo:table-cell>
                                </fo:table-row>
                            </th:block>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
            </th:block>


            <!-- No Data Case -->
            <th:block th:if="${model.body.summarySheet == null or #lists.isEmpty(model.body.summarySheet)}">
                <fo:block font-size="12pt" font-family="Times New Roman"
                          text-align="center" space-before="3mm">
                    No Summary Sheet data available
                </fo:block>
            </th:block>


            <!-- Child Better Section -->
            <fo:block font-size="14pt"
                      font-family="Times New Roman"
                      text-align="center"
                      font-weight="bold"
                      space-before="3mm"
                      space-after="2mm"
                      border-bottom="1pt solid black"
                      padding-bottom="1mm">
                Child Better
            </fo:block>

            <!-- Check if data exists -->
            <th:block th:if="${model.body.childBetter != null and #lists.size(model.body.childBetter) > 0}">
                <fo:block space-before="2mm" space-after="2mm" font-family="Times New Roman">
                    <fo:table table-layout="fixed" width="100%">
                        <fo:table-column column-width="50%"/>
                        <fo:table-column column-width="25%mm"/>
                        <fo:table-column column-width="25%"/>

                        <!-- Header Row -->
                        <fo:table-header>
                            <fo:table-row font-weight="bold" background-color="#f2f2f2">
                                <fo:table-cell border="0.5pt solid black" padding-left="-3pt" text-align="left" padding-top="3pt" padding-bottom="3pt" margin-left="10pt">
                                    <fo:block>Name</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="0.5pt solid black" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" text-align="left" margin-left="10pt">
                                    <fo:block>Term 1</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="0.5pt solid black" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" text-align="left" margin-left="10pt">
                                    <fo:block>Term 2</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>

                        <!-- Body Rows -->
                        <fo:table-body>
                            <th:block th:each="subject : ${model.body.childBetter}">
                                <fo:table-row>
                                    <fo:table-cell border="0.5pt solid black" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" text-align="left" margin-left="10pt">
                                        <fo:block th:text="${subject.name}"/>
                                    </fo:table-cell>
                                    <fo:table-cell font-size="9pt" border="0.5pt solid black" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" text-align="left" margin-left="10pt">
                                        <fo:block th:text="${subject.term1}"/>
                                    </fo:table-cell>
                                    <fo:table-cell font-size="9pt" border="0.5pt solid black" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" text-align="left" margin-left="10pt">
                                        <fo:block th:text="${subject.term2}"/>
                                    </fo:table-cell>
                                </fo:table-row>
                            </th:block>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
            </th:block>


            <!-- No Data Case -->
            <th:block th:if="${model.body.childBetter == null or #lists.isEmpty(model.body.childBetter)}">
                <fo:block font-size="12pt" font-family="Times New Roman"
                          text-align="center" space-before="3mm">
                    No Child Better data available
                </fo:block>
            </th:block>

            <!-- Student Interests Section -->
            <fo:block font-size="14pt"
                      font-family="Times New Roman"
                      text-align="center"
                      font-weight="bold"
                      space-before="3mm"
                      space-after="2mm"
                      border-bottom="1pt solid black"
                      padding-bottom="1mm">
                Student Interests
            </fo:block>

            <th:block th:if="${model.body.interestedActivities != null}">
                <fo:block space-before="2mm" space-after="2mm" font-size="10pt" font-family="Times New Roman">
                    <fo:table>
                        <fo:table-column column-width="35mm"/>
                        <fo:table-column column-width="60mm"/>
                        <fo:table-column column-width="40mm"/>
                        <fo:table-column column-width="60mm"/>

                        <fo:table-body>
                            <fo:table-row space-after="2mm">
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Reading:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.interestedActivities.reading ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Dancing:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.interestedActivities.dancing ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                            </fo:table-row>

                            <fo:table-row space-after="2mm">
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Singing:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.interestedActivities.singing ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Playing Instrument:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.interestedActivities.playingMusicalInstrument ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                            </fo:table-row>

                            <fo:table-row>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Sports / Games:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.interestedActivities.sportOrGames ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Creative Writing:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.interestedActivities.creativeWriting ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                            </fo:table-row>

                            <fo:table-row>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Gardening:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.interestedActivities.gardening ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Yoga:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.interestedActivities.yoga ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                            </fo:table-row>

                            <fo:table-row>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Art:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.interestedActivities.art ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Craft:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.interestedActivities.craft ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                            </fo:table-row>

                            <fo:table-row>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Cooking:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.interestedActivities.cooking ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Other:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.interestedActivities.other ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                            </fo:table-row>


                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Specify:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell number-columns-spanned="3">
                                    <fo:block th:text="${model.body.interestedActivities.other ? model.body.interestedActivities.otherSpecify : 'No'}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>

                    </fo:table>
                </fo:block>
            </th:block>
            <!-- No Data Case -->
            <th:block th:if="${model.body.interestedActivities == null}">
                <fo:block font-size="12pt" font-family="Times New Roman"
                          text-align="center" space-before="3mm">
                    No Interested Activities data available
                </fo:block>
            </th:block>


            <!-- Parent Facilitator Section -->
            <fo:block font-size="14pt"
                      font-family="Times New Roman"
                      text-align="center"
                      font-weight="bold"
                      space-before="3mm"
                      space-after="2mm"
                      border-bottom="1pt solid black"
                      padding-bottom="1mm">
                Parent Facilitator
            </fo:block>
            <fo:block font-size="12pt"
                      font-family="Times New Roman"
                      font-weight="bold"
                      space-before="1mm"
                      space-after="2mm">
                Resources available our child at home </fo:block>

            <th:block th:if="${model.body.parentFacilitator != null}">
                <fo:block space-before="2mm" space-after="2mm" font-size="10pt" font-family="Times New Roman" margin-left="3pt">
                    <fo:table>
                        <fo:table-column column-width="35mm"/>
                        <fo:table-column column-width="60mm"/>
                        <fo:table-column column-width="40mm"/>
                        <fo:table-column column-width="60mm"/>

                        <fo:table-body>
                            <fo:table-row space-after="2mm">
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Books:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.parentFacilitator.books ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Magazine:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.parentFacilitator.magazine ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                            </fo:table-row>

                            <fo:table-row space-after="2mm">
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Toy and Games:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.parentFacilitator.toyAndGames ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Tablet or Mobile:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.parentFacilitator.tabletOrMobile ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                            </fo:table-row>

                            <fo:table-row>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Laptop:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.parentFacilitator.laptop ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Internet:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.parentFacilitator.internet ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
            </th:block>


            <!-- No Data Case -->
            <th:block th:if="${model.body.parentFacilitator == null}">
                <fo:block font-size="12pt" font-family="Times New Roman"
                          text-align="center" space-before="3mm">
                    No Parent Facilitator data available
                </fo:block>
            </th:block>

            <!-- Parents Feedback Section -->
            <fo:block font-size="14pt"
                      font-family="Times New Roman"
                      text-align="center"
                      font-weight="bold"
                      space-before="3mm"
                      space-after="2mm"
                      border-bottom="1pt solid black"
                      padding-bottom="1mm">
                Parents Feedback
            </fo:block>

            <th:block th:if="${model.body.parentsFeedbacks != null and !model.body.parentsFeedbacks.isEmpty()}">
                <fo:block space-before="2mm" space-after="2mm" font-family="Times New Roman">

                    <fo:table table-layout="fixed" width="100%" border="0.5pt solid black">
                        <fo:table-column column-width="50%"/>
                        <fo:table-column column-width="25%"/>
                        <fo:table-column column-width="25%"/>

                        <fo:table-header>
                            <fo:table-row background-color="#f0f0f0">
                                <fo:table-cell border="0.5pt solid black" padding="2pt">
                                    <fo:block font-weight="bold">Name</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="0.5pt solid black" padding="2pt">
                                    <fo:block font-weight="bold">Term1</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="0.5pt solid black" padding="2pt">
                                    <fo:block font-weight="bold">Term2</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>

                        <fo:table-body>
                            <th:block th:each="item : ${model.body.parentsFeedbacks}">
                                <fo:table-row>
                                    <fo:table-cell border="0.5pt solid black" padding="2pt">
                                        <fo:block th:text="${item.name}"/>
                                    </fo:table-cell>
                                    <fo:table-cell font-size="9pt" border="0.5pt solid black" padding="2pt">
                                        <fo:block th:text="${item.term1}"/>
                                    </fo:table-cell>
                                    <fo:table-cell font-size="9pt" border="0.5pt solid black" padding="2pt">
                                        <fo:block th:text="${item.term2}"/>
                                    </fo:table-cell>
                                </fo:table-row>
                            </th:block>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
            </th:block>

            <!-- No Data Case -->
            <th:block th:if="${model.body.parentsFeedbacks == null or model.body.parentsFeedbacks.isEmpty()}">
                <fo:block font-size="12pt" font-family="Times New Roman"
                          text-align="center" space-before="3mm">
                    No Parent Facilitator Data Available
                </fo:block>
            </th:block>


            <!-- Child Support Section -->
            <fo:block font-size="14pt"
                      font-family="Times New Roman"
                      text-align="center"
                      font-weight="bold"
                      space-before="3mm"
                      space-after="2mm"
                      border-bottom="1pt solid black"
                      padding-bottom="1mm">
                Child Support
            </fo:block>
            <!-- Child Support -->
            <th:block th:if="${model.body.childSupports != null
                  and model.body.childSupports.childSupportDetails != null
                  and !model.body.childSupports.childSupportDetails.isEmpty()}">
                <fo:block space-before="2mm" space-after="2mm" font-family="Times New Roman">

                    <fo:table table-layout="fixed" width="100%" border="0.5pt solid black">
                        <fo:table-column column-width="75%"/>
                        <fo:table-column column-width="25%"/>

                        <fo:table-header>
                            <fo:table-row background-color="#f0f0f0">
                                <fo:table-cell border="0.5pt solid black" padding="2pt">
                                    <fo:block font-weight="bold">Name</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="0.5pt solid black" padding="2pt">
                                    <fo:block font-weight="bold">Value</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>

                        <fo:table-body>
                            <th:block th:each="item : ${model.body.childSupports.childSupportDetails}">
                                <fo:table-row>
                                    <fo:table-cell border="0.5pt solid black" padding="2pt">
                                        <fo:block th:text="${item.name}"/>
                                    </fo:table-cell>
                                    <fo:table-cell font-size="9pt" border="0.5pt solid black" padding="2pt">
                                        <fo:block th:text="${item.value != null ? (item.value ? 'Yes' : 'No') : ''}"/>
                                    </fo:table-cell>
                                </fo:table-row>
                            </th:block>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
            </th:block>

            <!-- No Data Case -->
            <th:block th:if="${model.body.childSupports == null
                  or model.body.childSupports.childSupportDetails == null
                  or model.body.childSupports.childSupportDetails.isEmpty()}">
                <fo:block font-size="10pt" font-family="Times New Roman"
                          text-align="center" space-before="3mm">
                    No Child Support Data Available
                </fo:block>
            </th:block>


            <!-- Facilitator Section -->
            <fo:block font-size="14pt"
                      font-family="Times New Roman"
                      text-align="center"
                      font-weight="bold"
                      space-before="3mm"
                      space-after="2mm"
                      border-bottom="1pt solid black"
                      padding-bottom="1mm">
                Facilitator
            </fo:block>

            <th:block th:if="${model.body.facilitator != null and model.body.facilitator.skills != null and !model.body.facilitator.skills.isEmpty()}">
                <fo:block space-before="2mm" space-after="2mm" font-family="Times New Roman">

                    <!-- Loop Subjects -->
                    <th:block th:each="subject : ${model.body.facilitator.skills}">
                        <!-- Subject Heading -->
                        <fo:block font-size="12pt" font-weight="bold"
                                  space-before="4mm" space-after="2mm" text-decoration="underline">
                            <fo:inline th:text="${subject.skillName}"/>
                        </fo:block>

                        <!-- Competency Table -->
                        <fo:table table-layout="fixed" width="100%" margin-left="6mm">
                            <fo:table-column column-width="93.5mm"/>
                            <fo:table-column column-width="45mm"/>
                            <fo:table-column column-width="45mm"/>

                            <!-- Header Row -->
                            <fo:table-header>
                                <fo:table-row font-weight="bold" background-color="#f2f2f2">
                                    <fo:table-cell border="0.5pt solid black" text-align="left" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" margin-left="10pt">
                                        <fo:block>Name</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="0.5pt solid black" text-align="left" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" margin-left="10pt">
                                        <fo:block>Term 1</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="0.5pt solid black" text-align="left" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" margin-left="10pt">
                                        <fo:block>Term 2</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-header>

                            <!-- Body Rows -->
                            <fo:table-body>
                                <th:block th:each="detail : ${subject.facilitatorDetails}">
                                    <fo:table-row>
                                        <fo:table-cell border="0.5pt solid black" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" text-align="left" margin-left="10pt">
                                            <fo:block th:text="${detail.name}"/>
                                        </fo:table-cell>
                                        <fo:table-cell font-size="9pt" border="0.5pt solid black" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" text-align="left" margin-left="10pt">
                                            <fo:block th:text="${detail.term1}"/>
                                        </fo:table-cell>
                                        <fo:table-cell font-size="9pt" border="0.5pt solid black" padding-left="-3pt" padding-top="3pt" padding-bottom="3pt" text-align="left" margin-left="10pt">
                                            <fo:block th:text="${detail.term2}"/>
                                        </fo:table-cell>
                                    </fo:table-row>
                                </th:block>
                            </fo:table-body>
                        </fo:table>
                    </th:block>
                </fo:block>
            </th:block>

            <!-- No Data Case -->
            <th:block th:if="${model.body.facilitator == null
                  or model.body.facilitator.skills == null
                  or model.body.facilitator.skills.isEmpty()}">
                <fo:block font-size="10pt" font-family="Times New Roman"
                          text-align="center" space-before="3mm">
                    No Facilitator Data Available
                </fo:block>
            </th:block>

            <fo:block space-before="6mm" space-after="2mm"
                      font-family="Times New Roman" font-size="12pt" font-weight="bold" text-decoration="underline">
                Area of Strength
            </fo:block>

            <th:block th:if="${model.body.facilitator != null and model.body.facilitator.areaOfStrength != null}">
                <fo:block space-before="2mm" space-after="2mm" font-size="10pt" font-family="Times New Roman" margin-left="3pt">
                    <fo:table>
                        <fo:table-column column-width="60mm"/>
                        <fo:table-column column-width="40mm"/>
                        <fo:table-column column-width="60mm"/>
                        <fo:table-column column-width="40mm"/>

                        <fo:table-body>
                            <fo:table-row space-after="2mm">
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Take Feedback Positively:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.facilitator.areaOfStrength.takeFeedbackPositively ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Work Independently:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.facilitator.areaOfStrength.workIndependently ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                            </fo:table-row>

                            <fo:table-row space-after="2mm">
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Effective Communication:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.facilitator.areaOfStrength.effectiveCommunication ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Solution-Focussed Thinking:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.facilitator.areaOfStrength.solutionsFocusedThinking ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                            </fo:table-row>

                            <fo:table-row>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Empathetic:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.facilitator.areaOfStrength.empathetic ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Organization &amp; Prioritization:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.facilitator.areaOfStrength.organizationAndPrioritization ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                            </fo:table-row>

                            <fo:table-row>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Collaborative Skills:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.facilitator.areaOfStrength.collaborativeSkills ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Responsible:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.facilitator.areaOfStrength.responsible ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                            </fo:table-row>

                            <fo:table-row>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Creative:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.facilitator.areaOfStrength.creative ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Concentration:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.facilitator.areaOfStrength.concentration ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                            </fo:table-row>

                            <fo:table-row>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Any Other:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.facilitator.areaOfStrength.anyOther ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Specify:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.facilitator.areaOfStrength.otherValue}"/>
                                </fo:table-cell>
                            </fo:table-row>

                        </fo:table-body>

                    </fo:table>
                </fo:block>
            </th:block>
            <!-- No Data Case -->
            <th:block th:if="${model.body.facilitator == null or model.body.facilitator.areaOfStrength == null}">
                <fo:block font-size="12pt" font-family="Times New Roman"
                          text-align="center" space-before="3mm">
                    No Area Of Strength data available
                </fo:block>
            </th:block>


            <fo:block space-before="6mm" space-after="2mm"
                      font-family="Times New Roman" font-size="12pt" font-weight="bold" text-decoration="underline">
                Barrier Of Success
            </fo:block>

            <th:block th:if="${model.body.facilitator != null and model.body.facilitator.barrierOfSuccess != null}">
                <fo:block space-before="2mm" space-after="2mm" font-size="10pt" font-family="Times New Roman" margin-left="3pt">
                    <fo:table>
                        <fo:table-column column-width="60mm"/>
                        <fo:table-column column-width="40mm"/>
                        <fo:table-column column-width="60mm"/>
                        <fo:table-column column-width="40mm"/>

                        <fo:table-body>
                            <fo:table-row space-after="2mm">
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Lack of Attention:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.facilitator.barrierOfSuccess.lackOfAttention ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Lack of Motivation:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.facilitator.barrierOfSuccess.lackOfMotivation ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                            </fo:table-row>

                            <fo:table-row space-after="2mm">
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Lack of Preparation:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.facilitator.barrierOfSuccess.lackOfPreparation ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Peer Pressure:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.facilitator.barrierOfSuccess.peerPressure ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                            </fo:table-row>

                            <fo:table-row>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Undefined Goals:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.facilitator.barrierOfSuccess.undefinedGoals ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Domestic Issues</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.facilitator.barrierOfSuccess.domesticIssues ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                            </fo:table-row>

                            <fo:table-row>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Inappropriate behaviour in classroom:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.facilitator.barrierOfSuccess.inappropriateBehaviourInClassRoom ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Severe illness or injury:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.facilitator.barrierOfSuccess.severeIllnessOfInjury ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                            </fo:table-row>

                            <fo:table-row>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>None:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.facilitator.barrierOfSuccess.none ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Any Other:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.facilitator.barrierOfSuccess.anyOther ? 'Yes' : 'No'}"/>
                                </fo:table-cell>
                            </fo:table-row>

                            <th:block th:if="${model.body.facilitator.barrierOfSuccess.anyOther}">
                                <fo:table-row>
                                    <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                        <fo:block>Specify:</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding-bottom="1mm" number-columns-spanned="3">
                                        <fo:block th:text="${model.body.facilitator.barrierOfSuccess.otherValue}"/>
                                    </fo:table-cell>
                                </fo:table-row>
                            </th:block>

                        </fo:table-body>

                    </fo:table>
                </fo:block>
            </th:block>

            <th:block th:if="${model.body.facilitator == null or model.body.facilitator.barrierOfSuccess == null}">
                <fo:block font-size="12pt" font-family="Times New Roman"
                          text-align="center" space-before="3mm">
                    No Barrier Of Success data available
                </fo:block>
            </th:block>

            <fo:block space-before="6mm" space-after="2mm"
                      font-family="Times New Roman" font-size="12pt" font-weight="bold" text-decoration="underline">
                Future Progress
            </fo:block>

            <th:block th:if="${model.body.facilitator != null}">
                <fo:block space-before="2mm" space-after="2mm" font-size="10pt" font-family="Times New Roman" margin-left="3pt">
                    <fo:table>
                        <fo:table-column column-width="60mm"/>
                        <fo:table-column column-width="40mm"/>
                        <fo:table-column column-width="60mm"/>
                        <fo:table-column column-width="40mm"/>

                        <fo:table-body>
                            <fo:table-row space-after="2mm">
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Future Step:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.facilitator.studentProgress}"/>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding-bottom="1mm">
                                    <fo:block>Future Steps:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-bottom="1mm">
                                    <fo:block th:text="${model.body.facilitator.futureSteps}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
            </th:block>

            <fo:block space-before="6mm" space-after="2mm"
                      font-family="Times New Roman" font-size="12pt"
                      font-weight="bold"
                      th:if="${model.body.facilitator != null}">
                Participation: <fo:inline font-weight="normal" font-size="10pt" text-decoration="none"
                                          th:text="${model.body.facilitator.participation}"/>
            </fo:block>

            <fo:block space-before="6mm" space-after="2mm"
                      font-family="Times New Roman" font-size="12pt"
                      font-weight="bold"
                      th:if="${model.body.facilitator != null}">
                Achievements: <fo:inline font-weight="normal" font-size="10pt" text-decoration="none"
                                         th:text="${model.body.facilitator.achievement}"/>
            </fo:block>

            <fo:block space-before="6mm" space-after="2mm"
                      font-family="Times New Roman" font-size="12pt"
                      font-weight="bold"
                      th:if="${model.body.facilitator != null}">
                Class Facilitator Remarks: <fo:inline font-weight="normal" font-size="10pt" text-decoration="none"
                                                      th:text="${model.body.facilitator.classFacilitatorRemarks}"/>
            </fo:block>

            <fo:block space-before="6mm" space-after="2mm"
                      font-family="Times New Roman" font-size="12pt"
                      font-weight="bold"
                      th:if="${model.body.facilitator != null}">
                Principle Remarks: <fo:inline font-weight="normal" font-size="10pt"
                                              th:text="${model.body.facilitator.principalRemarks}"/>
            </fo:block>


        </fo:flow>
    </fo:page-sequence>

</fo:root>
