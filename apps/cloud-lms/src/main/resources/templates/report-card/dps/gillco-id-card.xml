<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format"
         xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xsl="http://www.w3.org/1999/XSL/Transform">

    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice" page-height="210mm" page-width="297mm">
            <fo:region-body margin="12mm"/>
            <fo:region-before extent="10mm"/>
        </fo:simple-page-master>
    </fo:layout-master-set>

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <!-- Outer container -->
            <fo:block-container width="100%" height="93%" margin-top="0mm"  padding="6mm" position="relative">
                <fo:block-container absolute-position="absolute" top="20%" left="50%" width="100mm" height="100mm">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="100%" content-height="100%">
                            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 100 100">
                                <image x="0" y="0" width="100%" height="100%"
                                       xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/DPS.svg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <!--front side -->
                <fo:block-container absolute-position="absolute"
                                    top="20%" left="3%"
                                    width="12cm" height="8cm"
                                    border="1pt solid black" padding="4pt">

                    <!-- Title -->
                    <fo:block text-align="center" margin-left="15mm" font-size="14pt" font-weight="bold" space-after="3pt" padding-top="6pt">
                        PARENTS' IDENTITY CARD
                    </fo:block>
                    <fo:block text-align="center" margin-left="15mm" font-size="10pt" font-weight="bold" space-after="7pt">
                        SESSION <fo:inline th:text="${model.header.academicYear}"> </fo:inline>
                    </fo:block>
                    <!-- Student Information with Photo -->
                    <fo:table table-layout="fixed" width="100%" font-size="8pt" space-after="6pt">
                        <fo:table-column column-width="18%"/>
                        <fo:table-column column-width="56%"/>
                        <fo:table-column column-width="30%"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block space-after="2mm" font-size="8pt">
                                        Student's Name<fo:inline-container inline-progression-dimension="135pt"
                                                                           border-bottom="0.6pt solid black"
                                                                           text-align="center"
                                                                           display-align="center">
                                        <fo:block border-bottom="0.6pt solid black" font-size="7pt"
                                                  th:text="${model.header.studentName != null and model.header.studentName != '' ? model.header.studentName : ' '}">
                                            &#160;
                                        </fo:block>
                                    </fo:inline-container>
                                    </fo:block>
                                    <fo:block space-after="2mm" font-size="8pt">
                                        Roll No.<fo:inline-container inline-progression-dimension="52pt"
                                                                     border-bottom="0.6pt solid black"
                                                                     text-align="center"
                                                                     display-align="center">
                                        <fo:block text-align="center" border-bottom="0.6pt solid black" font-size="7pt"
                                                  th:text="${model.header.rollNo != null and model.header.rollNo != '' ? model.header.rollNo : ' '}">
                                            &#160;
                                        </fo:block>
                                    </fo:inline-container>
                                        Class &amp; Section<fo:inline-container inline-progression-dimension="52pt"
                                                                                border-bottom="0.6pt solid black"
                                                                                text-align="center"
                                                                                display-align="center">
                                        <fo:block text-align="center" border-bottom="0.6pt solid black" font-size="7pt"
                                                  th:text="${model.header.sectionName != null and model.header.sectionName != '' ? model.header.sectionName : ' '}">
                                            &#160;
                                        </fo:block>
                                    </fo:inline-container>
                                    </fo:block>
                                    <fo:block space-after="1mm" font-size="8pt">
                                        Admission No.<fo:inline-container inline-progression-dimension="45pt"
                                                                          border-bottom="0.6pt solid black"
                                                                          text-align="center"
                                                                          display-align="center">
                                        <fo:block text-align="center" border-bottom="0.6pt solid black" font-size="7pt"
                                                  th:text="${model.header.admissionNo != null and model.header.admissionNo != '' ? model.header.admissionNo : ' '}">
                                            &#160;
                                        </fo:block>
                                    </fo:inline-container>
                                        Date of Birth <fo:inline-container inline-progression-dimension="45pt"
                                                                           border-bottom="0.6pt solid black"
                                                                           text-align="center"
                                                                           display-align="center">
                                        <fo:block text-align="center" border-bottom="0.6pt solid black" font-size="7pt"
                                                  th:text="${model.header.dob != null and model.header.dob != '' ? model.header.dob : ' '}">
                                            &#160;
                                        </fo:block>
                                    </fo:inline-container>
                                    </fo:block>
                                    <fo:block space-after="2.2mm" font-size="8pt">
                                        Address<fo:inline-container inline-progression-dimension="161pt"
                                                                    border-bottom="0.6pt solid black"
                                                                    text-align="center"
                                                                    display-align="center">
                                        <fo:block border-bottom="0.6pt solid black" font-size="7pt"
                                                  th:text="${model.header.address != null and model.header.address != '' ? model.header.address : ' '}">
                                            &#160;
                                        </fo:block>
                                    </fo:inline-container>
                                        <fo:inline-container inline-progression-dimension="191pt"
                                                             border-bottom="0.6pt solid black"
                                                             text-align="center"
                                                             display-align="center">
                                            <fo:block border-bottom="0.6pt solid black" font-size="7pt" padding-top="1mm">
                                                &#160;
                                            </fo:block>
                                        </fo:inline-container>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="center" display-align="center">
                                    <fo:block-container border="1pt solid black" height="2.7cm" width="2.5cm" margin-top="-7mm">
                                        <fo:block display-align="center" text-align="center">
                                            <fo:instream-foreign-object content-width="80%" content-height="70%">
                                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                                     width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid meet">
                                                    <image x="0" y="0" width="100" height="100" preserveAspectRatio="none"
                                                           th:xlink:href="@{${model.header.studentPhoto}}"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                    <!-- Father / Mother / Guardian Section -->
                    <fo:table table-layout="fixed" width="100%" font-size="7pt" space-before="4pt">
                        <fo:table-column column-width="38%"/>
                        <fo:table-column column-width="31%"/>
                        <fo:table-column column-width="34%"/>
                        <fo:table-body>
                            <fo:table-row>
                                <!-- Father -->
                                <fo:table-cell>
                                    <fo:block text-align="center" font-weight="bold" margin-left="7mm">Father</fo:block>
                                    <fo:block-container margin-left="23mm" border="1pt solid black" height="2.3cm" width="2.2cm"
                                                        text-align="center" display-align="center">
                                        <fo:block display-align="center" text-align="center" margin-left="-23mm">
                                            <fo:instream-foreign-object content-width="60%" content-height="60%">
                                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                                     width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid meet">
                                                    <image x="0" y="0" width="100" height="100" preserveAspectRatio="none"
                                                           th:xlink:href="@{${model.header.fatherPhoto}}"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:block>
                                    </fo:block-container>
                                    <fo:block margin-top="2mm" margin-left="23mm">
                                        Name <fo:inline-container inline-progression-dimension="45pt"
                                                                  border-bottom="0.6pt solid black"
                                                                  text-align="center"
                                                                  display-align="center" >
                                        <fo:block border-bottom="0.6pt solid black" font-size="4pt" margin-left="-23mm" margin-top="-15mm"
                                                  th:text="${model.header.fatherName != null and model.header.fatherName != '' ? model.header.fatherName : ' '}">
                                            &#160;
                                        </fo:block>
                                    </fo:inline-container>
                                    </fo:block>
                                    <fo:block margin-top="1mm" margin-left="23mm">Mob.<fo:inline-container inline-progression-dimension="50pt"
                                                                                                           border-bottom="0.6pt solid black"
                                                                                                           text-align="center"
                                                                                                           display-align="center" >
                                        <fo:block border-bottom="0.6pt solid black" font-size="6pt" margin-left="-23mm" margin-top="-15mm"
                                                  th:text="${model.header.fatherNumber != null and model.header.fatherNumber != '' ? model.header.fatherNumber : ' '}">
                                            &#160;
                                        </fo:block>
                                    </fo:inline-container></fo:block>
                                </fo:table-cell>
                                <!-- Mother -->
                                <fo:table-cell>
                                    <fo:block text-align="center" font-weight="bold" margin-left="-26mm">Mother</fo:block>
                                    <fo:block-container margin-left="2mm" border="1pt solid black" height="2.3cm" width="2.2cm"
                                                        text-align="center" display-align="center">
                                        <fo:block display-align="center" text-align="center" margin-left="-3mm">
                                            <fo:instream-foreign-object content-width="60%" content-height="60%">
                                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                                     width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid meet">
                                                    <image x="0" y="0" width="100" height="100" preserveAspectRatio="none"
                                                           th:xlink:href="@{${model.header.motherPhoto}}"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:block>
                                    </fo:block-container>
                                    <fo:block margin-top="2mm"  margin-left="2mm"> Name <fo:inline-container inline-progression-dimension="45pt"
                                                                                                             border-bottom="0.6pt solid black"
                                                                                                             text-align="center"
                                                                                                             display-align="center" >
                                        <fo:block border-bottom="0.6pt solid black" font-size="4pt" margin-left="-2mm" margin-top="-15mm"
                                                  th:text="${model.header.motherName != null and model.header.motherName != '' ? model.header.motherName : ' '}">
                                            &#160;
                                        </fo:block>
                                    </fo:inline-container></fo:block>
                                    <fo:block margin-top="1mm" margin-left="2mm">Mob. <fo:inline-container inline-progression-dimension="48pt"
                                                                                                           border-bottom="0.6pt solid black"
                                                                                                           text-align="center"
                                                                                                           display-align="center" >
                                        <fo:block border-bottom="0.6pt solid black" font-size="6pt" margin-left="-2mm" margin-top="-15mm"
                                                  th:text="${model.header.motherNumber != null and model.header.motherNumber != '' ? model.header.motherNumber : ' '}">
                                            &#160;
                                        </fo:block>
                                    </fo:inline-container></fo:block>
                                </fo:table-cell>
                                <!-- Guardian -->
                                <fo:table-cell>
                                    <fo:block text-align="center" font-weight="bold"  margin-left="-51mm">Guardian</fo:block>
                                    <fo:block-container margin-left="-10mm" border="1pt solid black" height="2.3cm" width="2.2cm"
                                                        text-align="center" display-align="center">
                                        <fo:block display-align="center" text-align="center" margin-left="9mm">
                                            <fo:instream-foreign-object content-width="60%" content-height="60%">
                                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                                     width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid meet">
                                                    <image x="0" y="0" width="100" height="100" preserveAspectRatio="none"
                                                           th:xlink:href="@{${model.header.guardianPhoto}}"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:block>
                                    </fo:block-container>
                                    <fo:block margin-top="2mm" margin-left="-10mm"> Name <fo:inline-container inline-progression-dimension="45pt"
                                                                                                              border-bottom="0.6pt solid black"
                                                                                                              text-align="center"
                                                                                                              display-align="center" >
                                        <fo:block border-bottom="0.6pt solid black" font-size="4pt" margin-left="10mm" margin-top="-15mm"
                                                  th:text="${model.header.guardianName != null and model.header.guardianName != '' ? model.header.guardianName : ' '}">
                                            &#160;
                                        </fo:block>
                                    </fo:inline-container></fo:block>
                                    <fo:block margin-top="1mm" margin-left="-10mm">Mob.<fo:inline-container inline-progression-dimension="50pt"
                                                                                                            border-bottom="0.6pt solid black"
                                                                                                            text-align="center"
                                                                                                            display-align="center" >
                                        <fo:block border-bottom="0.6pt solid black" font-size="6pt" margin-left="10mm" margin-top="-15mm"
                                                  th:text="${model.header.guardianNumber != null and model.header.guardianNumber != '' ? model.header.guardianNumber : ' '}">
                                            &#160;
                                        </fo:block>
                                    </fo:inline-container></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial" margin-top="-83mm" margin-left="-99mm">
                        <fo:instream-foreign-object content-width="96%" content-height="96%">
                            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 100 100">
                                <image x="0" y="0" width="100%" height="100%"
                                       xlink:href="https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/gillcoband.jpg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial" margin-top="-40mm" margin-left="-99mm">
                        <fo:instream-foreign-object content-width="60%" content-height="60%">
                            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 100 100">
                                <image x="0" y="0" width="100%" height="100%"
                                       xlink:href="https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/GILLCO_INTERNATIONAL_SCHOOL_LOGO.svg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                    <!-- Principal -->
                    <fo:block padding-top="45mm" font-size="7pt" text-align="center" margin-left="-100mm">
                        PRINCIPAL
                    </fo:block>
                </fo:block-container>
                <!--back side -->
                <fo:block-container absolute-position="absolute"
                                    top="20%" left="55%"
                                    width="12cm" height="8cm"
                                    border="1pt solid black">

                    <fo:block font-family="Arial" font-size="9pt" padding="6pt">
                        <!-- Please Note -->
                        <fo:block font-weight="bold" font-size="10pt" space-after="4pt" margin-top="10mm" margin-left="10mm">
                            Please Note -
                        </fo:block>
                        <fo:list-block >
                            <fo:list-item >
                                <fo:list-item-label end-indent="4mm" margin-left="10mm">
                                    <fo:block>1.</fo:block>
                                </fo:list-item-label>
                                <fo:list-item-body start-indent="8mm" margin-left="13mm">
                                    <fo:block>It is mandatory to produce this card while collecting the child.</fo:block>
                                </fo:list-item-body>
                            </fo:list-item>
                            <fo:list-item>
                                <fo:list-item-label end-indent="4mm" margin-left="10mm">
                                    <fo:block>2.</fo:block>
                                </fo:list-item-label>
                                <fo:list-item-body start-indent="8mm" margin-left="13mm">
                                    <fo:block>It must be produced at the gate.</fo:block>
                                </fo:list-item-body>
                            </fo:list-item>

                        </fo:list-block>
                        <!-- School Info with Background Color -->
                        <fo:block-container absolute-position="absolute"
                                            bottom="0" left="2mm"  width="97%" height="1.8cm"
                                            background-color="#F6D649" margin-top="-5mm" padding-top="7mm" padding-left="2mm" padding-right="1.9mm">
                            <fo:block font-size="12pt" font-weight="bold"
                                      color="white" text-align="left" space-after="3pt" margin-left="10mm">
                                GILLCO INTERNATIONAL SCHOOL
                            </fo:block>
                            <fo:block font-size="9.2pt" color="white" text-align="left" margin-left="10mm">
                                Gillco Valley, Sector 127, Mohali &#160;
                                Tel: 0160-5048754, 82839 00097
                            </fo:block>
                            <fo:block font-size="9.2pt" color="white" text-align="left" margin-left="10mm">
                                Email: <EMAIL> &#160; | &#160; www.gillcoschool.com
                            </fo:block>
                        </fo:block-container>
                    </fo:block>
                </fo:block-container>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>
