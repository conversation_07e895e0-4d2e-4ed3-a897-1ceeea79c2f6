<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format">

    <!-- Layout Masters -->
    <fo:layout-master-set>

        <!-- First Page Master -->
        <fo:simple-page-master master-name="invoice">
            <fo:region-body margin="10mm"/>
        </fo:simple-page-master>

        <!-- Report Header + Table Master -->
        <fo:simple-page-master master-name="report-header" page-height="297mm" page-width="210mm" margin="10mm">
            <fo:region-body margin="15mm"/>
        </fo:simple-page-master>

    </fo:layout-master-set>

    <!-- FIRST PAGE: Invoice-style header -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">

            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" padding="6mm">

                <fo:block-container absolute-position="absolute" top="-45%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial" th:if="${(model.header.gradeSlug matches 'i|ii|iii')}">
                        <fo:instream-foreign-object content-width="285%" content-height="285%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="440%" height="440%" viewBox="0 0 100 100">
                                <image x="0" y="0" width="100%" height="100%" xlink:href="https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon6thto8th.jpg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial" th:if="${(model.header.gradeSlug matches 'ix')}">
                        <fo:instream-foreign-object content-width="285%" content-height="285%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="440%" height="440%" viewBox="0 0 100 100">
                                <image x="0" y="0" width="100%" height="100%" xlink:href="https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon6thto8th.jpg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>

                <fo:table border="none" width="100%">
                    <fo:table-column column-width="22mm"/>
                    <fo:table-column column-width="150mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell display-align="center">
                                <fo:block text-align="left" padding-top="-10mm">
                                    <fo:external-graphic
                                            src='url("https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon%20bharati%20report%20card/doonlogo1.png")'
                                            content-width="70px"
                                            content-height="auto"
                                            scaling="uniform"/>
                                </fo:block>
                            </fo:table-cell>

                            <fo:table-cell display-align="center">
                                <fo:block text-align="center" padding-top="-22mm" color="#11064C">
                                    <fo:instream-foreign-object content-width="100%" content-height="40mm">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" overflow="visible">
                                            <text x="50%" y="50%"  text-anchor="middle" dominant-baseline="middle" font-size="25pt" font-family="Montserrat, Arial, Helvetica, sans-serif" font-weight="bold" transform="scale(1,1.7)">
                                                DOON BHARTI PUBLIC SCHOOL
                                            </text>
                                            <text x="40%" y="105%" text-anchor="middle" dominant-baseline="middle" font-weight="bold" font-size="16pt" font-family="Montserrat, Arial, Helvetica, sans-serif" th:if="${(model.header.gradeSlug matches 'iii')}">
                                                SEHATPUR
                                            </text>
                                            <text x="40%" y="105%" text-anchor="middle" dominant-baseline="middle" font-weight="bold" font-size="16pt" font-family="Montserrat, Arial, Helvetica, sans-serif" th:if="${(model.header.gradeSlug matches 'ix')}">
                                                Durga Enclave, Sehatpur, Faridabad(Hr.)
                                            </text>
                                            <text x="30%" y="125%" text-anchor="middle" dominant-baseline="middle" font-weight="bold" font-size="16pt" font-family="Montserrat, Arial, Helvetica, sans-serif" th:if="${(model.header.gradeSlug matches 'ix')}">
                                                Affiliated to C.B.S.E
                                            </text>
                                        </svg>
                                    </fo:instream-foreign-object>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:block text-align="center" font-size="25pt" font-family="Times New Roman, serif" font-weight="bold" padding-top="-10mm" color="#11064C" th:if="${(model.header.gradeSlug matches 'i|ii|iii')}">
                    LEARNING VOYAGE
                </fo:block>

                <fo:block text-align="center" font-size="25pt" font-family="Times New Roman, serif" font-weight="bold" padding-top="-10mm" color="#11064C" th:if="${(model.header.gradeSlug matches 'ix')}">
                    PERFORMANCE PROFILE
                </fo:block>

                <fo:block font-weight="bold" text-align="center" font-size="12pt" font-family="Times New Roman, serif" padding-top="-2mm" >
                    Continuous &amp; Comprehensive Evaluation (CCE)
                </fo:block>

                <fo:block text-align="center" font-size="11pt" font-family="Montserrat, Arial, Helvetica, sans-serif" padding-top="5mm"
                          th:if="${(model.header.gradeSlug matches 'ix')}">
                    <fo:inline font-weight="bold">&#8226; Unit Test -1 </fo:inline>
                    <fo:inline padding-left="3mm" font-weight="bold">&#8226; Half Yearly Examination </fo:inline>
                    <fo:inline padding-left="3mm" font-weight="bold">&#8226; Unit Test -2 </fo:inline>
                    <fo:inline padding-left="3mm" font-weight="bold">&#8226; Yearly Examination</fo:inline>
                </fo:block>

                <fo:block text-align="center" font-size="11pt" font-family="Montserrat, Arial, Helvetica, sans-serif" padding-top="5mm" color="#11064C"
                          th:if="${(model.header.gradeSlug matches 'i|ii|iii')}">
                    <fo:inline font-weight="bold">&#8226; Launch Learning </fo:inline>
                    <fo:inline padding-left="3mm" font-weight="bold">&#8226; Mid Mastery Triumph </fo:inline>
                    <fo:inline padding-left="3mm" font-weight="bold">&#8226; Advancing Anchors </fo:inline>
                    <fo:inline padding-left="3mm" font-weight="bold">&#8226; Yearly Knowledge Voyage</fo:inline>
                </fo:block>

                <fo:block text-align="center" font-size="13pt" font-weight="bold"
                          space-after="5mm" space-before="5mm">
                    Session 20<fo:inline-container inline-progression-dimension="20pt"
                                                   border-bottom="0.6pt solid black"
                                                   text-align="center"
                                                   display-align="center">
                    <fo:block border-bottom="0.6pt solid black" th:text="${model.header.sessionStart != null and model.header.sessionStart != '' ? model.header.sessionStart : ' '}">
                        &#160;
                    </fo:block>
                </fo:inline-container>
                    - 20<fo:inline-container inline-progression-dimension="20pt"
                                             border-bottom="0.6pt solid black"
                                             text-align="center"
                                             display-align="center">
                    <fo:block border-bottom="0.6pt solid black" th:text="${model.header.sessionEnd != null and model.header.sessionEnd != '' ? model.header.sessionEnd : ' '}">
                        &#160;
                    </fo:block>
                </fo:inline-container>
                    Class<fo:inline-container inline-progression-dimension="120pt"
                                              border-bottom="0.6pt solid black"
                                              text-align="center"
                                              display-align="center">
                    <fo:block border-bottom="0.6pt solid black" th:text="${model.header.className != null and model.header.className != '' ? model.header.className : ' '}">
                        &#160;
                    </fo:block>
                </fo:inline-container>
                    Section
                    <fo:inline-container inline-progression-dimension="80pt"
                                         border-bottom="0.6pt solid black"
                                         text-align="center"
                                         display-align="center">
                        <fo:block border-bottom="0.6pt solid black" th:text="${model.header.sectionName != null and model.header.sectionName != '' ? model.header.sectionName : ' '}">
                            &#160;
                        </fo:block>
                    </fo:inline-container>
                </fo:block>

               <fo:block-container border="2.5pt solid black" padding="5mm" margin-top="5mm" margin-left="13mm" padding-right="-10mm">
                    <fo:block font-size="11pt" space-after="5mm" margin-left="-17mm">
                        Name : <fo:inline-container inline-progression-dimension="350pt"
                                                    border-bottom="0.6pt solid black"
                                                    text-align="center"
                                                    display-align="center">
                        <fo:block border-bottom="0.6pt solid black" th:text="${model.header.studentName != null and model.header.studentName != '' ? model.header.studentName : ' '}">
                            &#160;
                        </fo:block>
                    </fo:inline-container>
                    </fo:block>

                    <fo:block font-size="11pt" space-after="5mm" margin-left="-17mm">
                        Date of Birth : <fo:inline-container inline-progression-dimension="66pt"
                                                             border-bottom="0.6pt solid black"
                                                             text-align="center"
                                                             display-align="center">
                        <fo:block border-bottom="0.6pt solid black" th:text="${model.header.dateOfBirth != null and model.header.dateOfBirth != '' ? model.header.dateOfBirth : ' '}">
                            &#160;
                        </fo:block>
                    </fo:inline-container>
                        Admission No. <fo:inline-container inline-progression-dimension="66pt"
                                                           border-bottom="0.6pt solid black"
                                                           text-align="center"
                                                           display-align="center">
                        <fo:block border-bottom="0.6pt solid black" th:text="${model.header.admissionNo != null and model.header.admissionNo != '' ? model.header.admissionNo : ' '}">
                            &#160;
                        </fo:block>
                    </fo:inline-container>
                        Roll No. <fo:inline-container inline-progression-dimension="66pt"
                                                      border-bottom="0.6pt solid black"
                                                      text-align="center"
                                                      display-align="center">
                        <fo:block border-bottom="0.6pt solid black" th:text="${model.header.rollNo != null and model.header.rollNo != '' ? model.header.rollNo : ' '}">
                            &#160;
                        </fo:block>
                    </fo:inline-container>
                    </fo:block>

                    <fo:block font-size="11pt" space-after="5mm" margin-left="-17mm">
                        Father / Guardian’s Name : <fo:inline-container inline-progression-dimension="255pt"
                                                                        border-bottom="0.6pt solid black"
                                                                        text-align="center"
                                                                        display-align="center">
                        <fo:block border-bottom="0.6pt solid black" th:text="${model.header.fatherName != null and model.header.fatherName != '' ? model.header.fatherName : ' '}">
                            &#160;
                        </fo:block>
                    </fo:inline-container>
                    </fo:block>
                    <fo:block font-size="11pt" space-after="5mm" margin-left="-17mm">
                        Residence Address : <fo:inline-container inline-progression-dimension="285pt"
                                                                 border-bottom="0.6pt solid black"
                                                                 text-align="center"
                                                                 display-align="center">
                        <fo:block border-bottom="0.6pt solid black" th:text="${model.header.address != null and model.header.address != '' ? model.header.address : ' '}">
                            &#160;
                        </fo:block>
                    </fo:inline-container>
                    </fo:block>

                    <fo:block font-size="11pt" space-after="5mm" margin-left="-17mm">
                        <fo:inline-container inline-progression-dimension="390pt"
                                             border-bottom="0.6pt solid black"
                                             text-align="center"
                                             display-align="center">
                            <fo:block border-bottom="0.6pt solid black" >
                                &#160;
                            </fo:block>
                        </fo:inline-container>
                    </fo:block>
                   <fo:block font-size="11pt" space-after="5mm" margin-left="-17mm">
                       Telephone Number : <fo:inline-container inline-progression-dimension="285pt"
                                                               border-bottom="0.6pt solid black"
                                                               text-align="center"
                                                               display-align="center">
                       <fo:block border-bottom="0.6pt solid black" th:text="${model.header.mobileNo != null and model.header.mobileNo != '' ? model.header.mobileNo : ' '}">
                           &#160;
                       </fo:block>
                    </fo:inline-container>
                   </fo:block>
                </fo:block-container>

            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>


    <!--second page -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm"  padding="6mm">
                <fo:block-container absolute-position="absolute" top="-45%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial" th:if="${(model.header.gradeSlug matches 'i|ii|iii')}">
                        <fo:instream-foreign-object content-width="285%" content-height="285%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="440%" height="440%" viewBox="0 0 100 100">
                                <image x="0" y="0" width="100%" height="100%" xlink:href="https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon6thto8th.jpg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial" th:if="${(model.header.gradeSlug matches 'ix')}">
                        <fo:instream-foreign-object content-width="285%" content-height="285%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="440%" height="440%" viewBox="0 0 100 100">
                                <image x="0" y="0" width="100%" height="100%" xlink:href="https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon6thto8th.jpg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:block margin-left="-8mm" padding-top="-5mm">
                    <fo:external-graphic src='url("https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon-logo-with-name.png")'
                                         content-width="50px" scaling="non-uniform"  />
                </fo:block>
                <fo:block-container absolute-position="absolute" top="-10mm" left="20mm" width="150mm" color="11064C">
                    <fo:block  font-size="15pt" font-weight="bold" text-align="center" space-after="5mm">
                        Session 20<fo:inline-container inline-progression-dimension="20pt"
                                                       border-bottom="0.6pt solid black"
                                                       text-align="center"
                                                       display-align="center">
                        <fo:block border-bottom="0.6pt solid black" th:text="${model.header.sessionStart != null and model.header.sessionStart != '' ? model.header.sessionStart : ' '}">
                            &#160;
                        </fo:block>
                    </fo:inline-container>
                        - 20<fo:inline-container inline-progression-dimension="20pt"
                                                 border-bottom="0.6pt solid black"
                                                 text-align="center"
                                                 display-align="center">
                        <fo:block border-bottom="0.6pt solid black" th:text="${model.header.sessionEnd != null and model.header.sessionEnd != '' ? model.header.sessionEnd : ' '}">
                            &#160;
                        </fo:block>
                    </fo:inline-container>
                    </fo:block>
                    <fo:block  font-size="12pt">
                        Full Name:<fo:inline-container inline-progression-dimension="350pt"
                                                       border-bottom="0.6pt solid black"
                                                       text-align="center"
                                                       display-align="center">
                        <fo:block border-bottom="0.6pt solid black" th:text="${model.header.studentName != null and model.header.studentName != '' ? model.header.studentName : ' '}">
                            &#160;
                        </fo:block>
                    </fo:inline-container>
                    </fo:block>
                    <fo:block  font-size="12pt" space-before="4mm" space-after="150mm">
                        Class: <fo:inline-container inline-progression-dimension="160pt"
                                                    border-bottom="0.6pt solid black"
                                                    text-align="center"
                                                    display-align="center">
                        <fo:block border-bottom="0.6pt solid black" th:text="${model.header.className != null and model.header.className != '' ? model.header.className : ' '}">
                            &#160;
                        </fo:block>
                    </fo:inline-container>
                        Section: <fo:inline-container inline-progression-dimension="160pt"
                                                      border-bottom="0.6pt solid black"
                                                      text-align="center"
                                                      display-align="center">
                        <fo:block border-bottom="0.6pt solid black" th:text="${model.header.sectionName != null and model.header.sectionName != '' ? model.header.sectionName : ' '}">
                            &#160;
                        </fo:block>
                    </fo:inline-container>
                    </fo:block>
                </fo:block-container>

                <fo:block text-align="center" font-size="25pt" font-family="Times New Roman, serif" font-weight="bold" padding-top="5mm" color="#11064C" >
                    LEARNING VOYAGE
                </fo:block>
                <fo:block text-align="center" font-size="11pt" font-family="Montserrat, Arial, Helvetica, sans-serif" padding-top="5mm" >
                    <fo:inline font-weight="bold">&#8226; Launchpad Learning </fo:inline>
                    <fo:inline padding-left="3mm" font-weight="bold">&#8226; Mid Mastery Triumph </fo:inline>
                    <fo:inline padding-left="3mm" font-weight="bold">&#8226; Advancing Anchors </fo:inline>
                    <fo:inline padding-left="3mm" font-weight="bold">&#8226; Yearly Knowledge Voyage</fo:inline>
                </fo:block>

                    <fo:table table-layout="fixed" border="1pt solid black" width="100%" space-before="5mm">
                        <fo:table-column column-width="30mm"/>
                        <fo:table-column column-width="30mm"/>
                        <fo:table-column column-width="20mm"/>
                        <fo:table-column column-width="25mm"/>
                        <fo:table-column column-width="25mm"/>
                        <fo:table-column column-width="20mm"/>
                        <fo:table-column column-width="20mm"/>
                        <fo:table-column column-width="20mm"/>
                        <fo:table-body>
                            <fo:table-row >
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block  padding-top="2mm" margin-left="2mm" font-weight="bold">Scholastic Area</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" number-rows-spanned="3">
                                    <fo:block text-align="center" font-weight="bold" padding-top="2mm">Launchpad learning (Grade)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" number-columns-spanned="6">
                                    <fo:block text-align="center" font-weight="bold" padding-top="2mm">Mid Mastery Triumph</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row >
                                <fo:table-cell border="1.5pt solid black" number-rows-spanned="2">
                                    <fo:block  padding-top="2mm" margin-left="2mm" font-weight="bold">Subject Name</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" number-rows-spanned="2">
                                    <fo:block text-align="center" font-weight="bold" padding-top="2mm">Portfolio (10)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" number-columns-spanned="2">
                                    <fo:block text-align="center" font-weight="bold" padding-top="2mm">Sub Enrichment Activity</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" number-rows-spanned="2">
                                    <fo:block text-align="center" font-weight="bold" padding-top="2mm">Mid Mastery triumph (70)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" number-rows-spanned="2">
                                    <fo:block text-align="center" font-weight="bold" padding-top="2mm">Total out of</fo:block>
                                    <fo:block text-align="center" font-weight="bold" padding-top="2mm">100</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" number-rows-spanned="2">
                                    <fo:block text-align="center" font-weight="bold" padding-top="5mm">Grade</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row >
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block  padding-top="2mm" margin-left="2mm" font-weight="bold" font-size="10pt">Cognitive Development  (10)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center" font-weight="bold" padding-top="2mm">Ma </fo:block>
                                    <fo:block text-align="center" font-weight="bold" padding-top="2mm">(10) </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row text-align="center" height="10mm" th:each="marks : ${model.body.firstTable.marks}">
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block  padding-top="2mm" th:text="${marks.subjectName}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block  padding-top="2mm" th:text="${marks.launchpadLearningGrade}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block  padding-top="2mm" th:text="${marks.portFolio}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block  padding-top="2mm" th:text="${marks.competitiveDevelopment}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block  padding-top="2mm" th:text="${marks.ma}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block  padding-top="2mm" th:text="${marks.midMasteryTriumph}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block  padding-top="2mm" th:text="${marks.total}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block  padding-top="2mm" th:text="${marks.grade}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>

                <fo:table table-layout="fixed" border="0.5pt solid black" width="100%" space-before="15mm">
                    <fo:table-column column-width="30mm"/>
                    <fo:table-column column-width="77.5mm"/>
                    <fo:table-column column-width="77.5mm"/>
                    <fo:table-body>
                        <fo:table-row height="5mm">
                            <fo:table-cell border="1.5pt solid black"  number-rows-spanned="2">
                                <fo:block text-align="center" font-weight="bold" padding-top="9mm">REMARKS</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1.5pt solid black">
                                <fo:block text-align="center" padding-top="0.7mm" font-weight="bold">Mid Mastery Triumph</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1.5pt solid black">
                                <fo:block text-align="center" padding-top="0.7mm" font-weight="bold">Yearly Knowledge voyage</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row height="15mm">
                            <fo:table-cell border="1.5pt solid black">
                                <fo:block text-align="center" font-weight="bold"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1.5pt solid black">
                                <fo:block text-align="center" font-weight="bold"> </fo:block>
                            </fo:table-cell>
                        </fo:table-row>

                        <fo:table-row height="10mm" text-align="center">
                            <fo:table-cell border="1.5pt solid black">
                                <fo:block padding-top="1mm">Teacher’s Signature</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1.5pt solid black"><fo:block> </fo:block></fo:table-cell>
                            <fo:table-cell border="1.5pt solid black"><fo:block> </fo:block></fo:table-cell>
                        </fo:table-row>

                        <fo:table-row height="10mm" text-align="center">
                            <fo:table-cell border="1.5pt solid black">
                                <fo:block padding-top="1mm">Supervisor’s Signature</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1.5pt solid black"><fo:block> </fo:block></fo:table-cell>
                            <fo:table-cell border="1.5pt solid black"><fo:block> </fo:block></fo:table-cell>
                        </fo:table-row>

                        <fo:table-row height="10mm" text-align="center">
                            <fo:table-cell border="1.5pt solid black">
                                <fo:block padding-top="1mm">Incharge’s Signature</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1.5pt solid black"><fo:block> </fo:block></fo:table-cell>
                            <fo:table-cell border="1.5pt solid black"><fo:block> </fo:block></fo:table-cell>
                        </fo:table-row>

                        <fo:table-row height="10mm" text-align="center">
                            <fo:table-cell border="1.5pt solid black">
                                <fo:block padding-top="1mm">Principal’s Signature</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1.5pt solid black"><fo:block> </fo:block></fo:table-cell>
                            <fo:table-cell border="1.5pt solid black"><fo:block> </fo:block></fo:table-cell>
                        </fo:table-row>

                        <fo:table-row height="10mm" text-align="center">
                            <fo:table-cell border="1.5pt solid black">
                                <fo:block padding-top="1mm">Guardian’s Signature</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1.5pt solid black"><fo:block> </fo:block></fo:table-cell>
                            <fo:table-cell border="1.5pt solid black"><fo:block> </fo:block></fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>


    <!-- THIRD PAGE -->

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm"  padding="6mm">
                <fo:block-container absolute-position="absolute" top="-45%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="285%" content-height="285%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="440%" height="440%" viewBox="0 0 100 100">
                                <image x="0" y="0" width="100%" height="100%" xlink:href="https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon6thto8th.jpg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:block margin-left="160mm" padding-top="-10mm">
                    <fo:external-graphic src='url("https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon-logo-with-name.png")'
                                         content-width="40px" scaling="non-uniform"  />
                </fo:block>

                <fo:block-container absolute-position="absolute" top="0mm" left="20mm" width="150mm">
                    <fo:block  font-size="15pt" font-weight="bold" text-align="center" space-after="5mm" color="#11064C">
                        Session 20<fo:inline border-bottom="0.6pt solid black" padding-left="6pt" padding-right="6pt"
                                             th:text="${model.header.sessionStart != null and model.header.sessionStart != '' ? model.header.sessionStart : ' '}"> &#160;</fo:inline>
                        - 20<fo:inline border-bottom="0.6pt solid black" padding-left="6pt" padding-right="6pt"
                                       th:text="${model.header.sessionStart != null and model.header.sessionStart != '' ? model.header.sessionStart : ' '}"> &#160;</fo:inline>
                    </fo:block>
                </fo:block-container>

                <!-- Main Examination Table -->
                <fo:block-container absolute-position="absolute" top="12mm" left="-5mm" width="190mm">
                    <fo:table table-layout="fixed" border="1pt solid black" width="100%">
                        <fo:table-column column-width="100mm"/>
                        <fo:table-column column-width="50mm"/>
                        <fo:table-column column-width="50mm"/>
                        <fo:table-body>
                            <fo:table-row >
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block  padding-top="2mm" margin-left="2mm" font-weight="bold">Child Care Development on 5 Point Grading Scale</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center" font-weight="bold" padding-top="2mm">Mid Mastery Triumph</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center" font-weight="bold" padding-top="2mm">Yearly knowledge voyage</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row th:each="marks : ${model.body.secondTable.marks}">
                                <fo:table-cell border="1.5pt solid black">
                                    <fo:block padding-top="2mm" margin-left="2mm" th:text="${marks.subjectName}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black">
                                    <fo:block text-align="center" padding-top="2mm" th:text="${marks.midMasteryTriumph}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black">
                                    <fo:block text-align="center" padding-top="2mm" th:text="${marks.yearlyKnowledgeVoyage}"> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block-container>

            <fo:block-container padding-top="155mm">
                <fo:block font-family="Arial" font-size="12pt" line-height="12pt" space-before="6mm">
                    <fo:table table-layout="fixed" width="200mm" border="0pt">
                        <fo:table-column column-width="110mm"/>
                        <fo:table-column column-width="100mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block>
                                        <fo:inline>Participated in class events</fo:inline>
                                        <fo:inline>
                                            <fo:leader leader-pattern="space" leader-length="25pt"/>
                                        </fo:inline>Yes
                                        <fo:inline border="0.8pt solid #000" width="8pt" height="11pt" font-family="ZapfDingbats"
                                                   th:if="${model.body.isParticipatedInInternal == 'true'}" text-align="center" display-align="center"> ✔
                                        </fo:inline>
                                        <fo:inline border="0.8pt solid #000" width="11pt" height="11pt" font-family="ZapfDingbats"
                                                   th:if="${model.body.isParticipatedInInternal == 'false'}"> &#160;&#160;
                                        </fo:inline>
                                        <fo:inline>
                                            <fo:leader leader-pattern="space" leader-length="25pt"/>
                                        </fo:inline>No
                                        <fo:inline border="0.8pt solid #000" width="11pt" height="11pt" font-family="ZapfDingbats"
                                                   th:if="${model.body.isParticipatedInInternal == 'true'}" text-align="center" display-align="center"> &#160;&#160;
                                        </fo:inline>
                                        <fo:inline border="0.8pt solid #000" width="11pt" height="11pt" font-family="ZapfDingbats"
                                                   th:if="${model.body.isParticipatedInInternal == 'false'}"> ✔
                                        </fo:inline>
                                    </fo:block>
                                    <fo:block space-before="2mm">
                                        <fo:inline>Participated in Olympiad</fo:inline>
                                        <fo:inline>
                                            <fo:leader leader-pattern="space" leader-length="62pt"/>
                                        </fo:inline>
                                        Yes
                                        <fo:inline border="0.8pt solid #000" width="8pt" height="11pt" font-family="ZapfDingbats"
                                                   th:if="${model.body.isParticipated == 'true'}" text-align="center" display-align="center"> ✔
                                        </fo:inline>
                                        <fo:inline border="0.8pt solid #000" width="11pt" height="11pt" font-family="ZapfDingbats"
                                                   th:if="${model.body.isParticipated == 'false'}"> &#160;&#160;
                                        </fo:inline>
                                        <fo:inline>
                                            <fo:leader leader-pattern="space" leader-length="25pt"/>
                                        </fo:inline>No
                                        <fo:inline border="0.8pt solid #000" width="8pt" height="11pt" font-family="ZapfDingbats"
                                                   th:if="${model.body.isParticipated == 'true'}" text-align="center" display-align="center"> &#160;&#160;
                                        </fo:inline>
                                        <fo:inline border="0.8pt solid #000" width="11pt" height="11pt" font-family="ZapfDingbats"
                                                   th:if="${model.body.isParticipated == 'false'}"> ✔
                                        </fo:inline>
                                    </fo:block>
                                </fo:table-cell>

                                <fo:table-cell>
                                    <fo:block>
                                        Subjects .................................................
                                    </fo:block>
                                    <fo:block space-before="2mm">
                                        Subjects .................................................
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>

                    <fo:table table-layout="fixed" width="180mm" space-before="6mm" font-size="9pt">
                        <fo:table-column column-width="35mm"/>
                        <fo:table-column column-width="45mm"/>
                        <fo:table-column column-width="20mm"/>
                        <fo:table-column column-width="80mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block border="0.8pt solid #000"  font-weight="bold" text-align="center">
                                        Total Working Days
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block border="0.8pt solid #000"  font-weight="bold" text-align="center">
                                        Total Attendance By Student
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-size="11pt">
                                    <fo:block>
                                        Health Status : Height <fo:inline border-bottom="0.6pt dotted black" padding-left="8pt" padding-right="8pt"
                                                                          th:text="${model.body.height != null and model.body.height != '' ? model.body.height : ' '}"> &#160;</fo:inline>
                                        &#160;&#160;&#160;&#160;<fo:inline border-bottom="0.6pt dotted black" padding-left="8pt" padding-right="8pt"
                                                                           th:text="${model.body.height != null and model.body.height != '' ? model.body.height : ' '}"> &#160;</fo:inline>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block border="0.8pt solid #000"  font-weight="bold" text-align="center"
                                              th:text="${model.body.totalWorkingDays}">
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block border="0.8pt solid #000"  font-weight="bold" text-align="center"
                                              th:text="${model.body.totalPresentDays}">
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-size="11pt">
                                    <fo:block margin-left="24mm">
                                        weight  <fo:inline border-bottom="0.6pt dotted black" padding-left="8pt" padding-right="8pt"
                                                           th:text="${model.body.weight != null and model.body.weight != '' ? model.body.weight : ' '}"> &#160;</fo:inline>
                                        &#160;&#160;&#160;&#160;<fo:inline border-bottom="0.6pt dotted black" padding-left="8pt" padding-right="8pt"
                                                                           th:text="${model.body.weight != null and model.body.weight != '' ? model.body.weight : ' '}"> &#160;</fo:inline>
                                    </fo:block>

                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                    <fo:block space-before="6mm" >
                        Overall Remarks (Year End)<fo:inline border-bottom="0.6pt dotted black" padding-left="162pt" padding-right="162pt"
                                                             th:text="${model.body.overallRemark != null and model.body.overallRemark != '' ? model.body.overallRemark : ' '}"> &#160;</fo:inline>
                    </fo:block>
                    <fo:block space-before="2mm" space-after="2mm">
                        <fo:inline border-bottom="0.6pt dotted black" padding-left="243pt" padding-right="243pt"
                        > &#160;</fo:inline>
                    </fo:block>
                    <fo:table table-layout="fixed" width="180mm" space-before="12mm">
                        <fo:table-column column-width="96mm"/>
                        <fo:table-column column-width="42mm"/>
                        <fo:table-column column-width="42mm"/>
                        <fo:table-body>
                            <fo:table-row>

                                <!-- Promotion text with check box -->
                                <fo:table-cell>
                                    <fo:block>
                                        Promoted to class
                                        <fo:leader leader-pattern="dots" leader-length="35%"/>
                                    </fo:block>
                                    <fo:block space-before="2mm" font-size="10pt">
                                        Or Eligible for improvement of performance
                                        <fo:inline padding-left="6pt"/>
                                        <fo:inline border="0.8pt solid #000" width="11pt" height="11pt" font-family="ZapfDingbats">
                                            <fo:leader leader-pattern="space" leader-length="9pt"/>
                                        </fo:inline>
                                    </fo:block>
                                </fo:table-cell>

                                <!-- Class teacher sign -->
                                <fo:table-cell>
                                    <fo:block text-align="center" space-before="6mm" >
                                        <fo:leader leader-pattern="dots" leader-length="90%"/>
                                    </fo:block>
                                    <fo:block text-align="center" font-size="10pt" space-before="1mm" >
                                        Class teacher’s Signature
                                    </fo:block>
                                </fo:table-cell>

                                <!-- Principal sign -->
                                <fo:table-cell>
                                    <fo:block text-align="center" space-before="6mm">
                                        <fo:leader leader-pattern="dots" leader-length="90%"/>
                                    </fo:block>
                                    <fo:block text-align="center" font-size="10pt" space-before="1mm">
                                        Principal Signature
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>

                </fo:block>
            </fo:block-container>

            </fo:block-container>
      </fo:flow>
    </fo:page-sequence>


    <!-- FOURTH PAGE: INFORMATION -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">

            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" padding="6mm">

                <!-- Background image -->
                <fo:block-container absolute-position="absolute" top="-45%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" th:if="${(model.header.gradeSlug matches 'ix')}">
                        <fo:instream-foreign-object content-width="285%" content-height="285%">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                 xmlns:xlink="http://www.w3.org/1999/xlink"
                                 width="440%" height="440%" viewBox="0 0 100 100">
                                <image x="0" y="0" width="100%" height="100%"
                                       xlink:href="https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon6thto8th.jpg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                    <fo:block text-align="center" th:if="${(model.header.gradeSlug matches 'i|ii|iii')}">
                        <fo:instream-foreign-object content-width="285%" content-height="285%">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                 xmlns:xlink="http://www.w3.org/1999/xlink"
                                 width="440%" height="440%" viewBox="0 0 100 100">
                                <image x="0" y="0" width="100%" height="100%"
                                       xlink:href="C:\Users\<USER>\Documents\Desktop\WhatsApp Image 2025-08-21 at 14.01.03_83b5a2d4.jpg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>

                <!-- Header row: LOGO left + INFORMATION pill right -->
                <fo:table border="none" width="100%">
                    <fo:table-column column-width="22mm"/>
                    <fo:table-column column-width="150mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <!-- Logo -->
                            <fo:table-cell display-align="center">
                                <fo:block text-align="left" padding-top="-7mm">
                                    <fo:external-graphic
                                            src='url("https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon%20bharati%20report%20card/doonlogo1.png")'
                                            content-width="70px"
                                            scaling="uniform"/>
                                </fo:block>
                            </fo:table-cell>




                            <!-- "INFORMATION" pill (simplified, no <defs>) -->
                            <fo:table-cell display-align="center" padding-left="--40mm">
                                <fo:block text-align="center" padding-top="-8mm">
                                    <fo:block-container width="50%" background-color="#11064C" padding="1mm">
                                        <fo:block font-size="18pt" font-weight="bold" text-align="center" color="white">
                                            INFORMATION
                                        </fo:block>
                                    </fo:block-container>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:block th:if="${(model.header.gradeSlug matches 'ix')}">
                    <!-- Bullet points -->
                    <fo:block font-size="13pt"  font-family="Times New Roman, serif" space-before="4mm" space-after="3mm">
                        &#8226; This Performance Profile is a continuous &amp; Comprehensive assessment of child
                        throughout the year in scholastic &amp; co-scholastic field.
                    </fo:block>

                    <fo:block font-size="13pt" font-family="Times New Roman, serif" space-after="3mm">
                        &#8226; We at DBPS, believe in preparing students to succeed in life. Discipline, punctuality
                        &amp; smart hard work are the pillars of success attained through passion, commitment
                        &amp; dedication.
                    </fo:block>

                    <fo:block font-size="13pt"  font-family="Times New Roman, serif" space-after="3mm">
                        &#8226; This performance profile is an important document related to your ward &amp; it is
                        required to be sent back to school within two working days of its receipt.
                    </fo:block>

                    <fo:block font-size="13pt"  font-family="Times New Roman, serif" space-after="3mm">
                        &#8226; Scholastic areas are judged in 7 point grading systems i.e. A1, A2, B1, B2, C1, C2, &amp; D
                    </fo:block>

                    <!-- 7-point grading table -->
                    <fo:table border="1.5pt solid black"  width="80mm"
                              margin-left="18mm" margin-top="2mm" space-after="6mm"
                              font-size="13pt" >
                        <fo:table-column column-width="50mm"/>
                        <fo:table-column column-width="50mm"/>
                        <fo:table-header text-align="center" font-weight="bold">
                            <fo:table-row background-color="#f2f2f2">
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">Grade</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">Marks Range</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body text-align="center">
                            <fo:table-row>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">A1</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block text-align="center">91-100</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">A2</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black">
                                    <fo:block text-align="center">81-90</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">B1</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">71-80</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">B2</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">61-70</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">C1</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">51-60</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">C2</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">45-50</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">D</fo:block>
                                </fo:table-cell><fo:table-cell border="1.5pt solid black" >
                                <fo:block text-align="center">33-40</fo:block>
                            </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>

                    <fo:block font-size="13pt"  font-family="Times New Roman, serif" space-after="3mm">
                        &#8226; Child Care Development is also assessed under 7 point grading system as above.
                    </fo:block>

                    <fo:block font-size="13pt" font-family="Times New Roman, serif" space-after="3mm">
                        &#8226; Co-scholastic areas are assessed under 5 point grading system as above.
                    </fo:block>

                    <!-- 5-point grading table -->
                    <fo:table border="1.5pt solid black" table-layout="fixed" width="80mm"
                              margin-left="18mm" margin-top="2mm" space-after="8mm"
                              font-size="13pt">
                        <fo:table-column column-width="50mm"/>
                        <fo:table-column column-width="50mm"/>
                        <fo:table-header text-align="center" font-weight="bold">
                            <fo:table-row background-color="#f2f2f2">
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">Grade</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">Connotation</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body text-align="center">
                            <fo:table-row>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">&#160;</fo:block>
                                </fo:table-cell><fo:table-cell border="1.5pt solid black" >
                                <fo:block text-align="center">&#160;</fo:block>
                            </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">&#160;</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">&#160;</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">&#160;</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">&#160;</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>

                    <!-- DBPS program -->
                    <fo:block font-size="13pt"  font-family="Times New Roman, serif" space-after="12mm">
                        &#8226; At DBPS, we work at grass-root level through our Connecting The Roots Program
                        which encircles learning of Music, Dance, ABACUS, Art &amp; Craft, Drawing,
                        Meditation, Vedik Shiksha, Sports, Games, Projects, Practical experiences, Labs,
                        various digitals &amp; 5 senses oriented activities in academic &amp; co-curricular field.
                    </fo:block>
                </fo:block>

                <fo:block th:if="${(model.header.gradeSlug matches 'i|ii|iii')}">
                    <!-- Bullet points -->
                    <fo:block font-size="13pt"  font-family="Times New Roman, serif" space-before="4mm" space-after="3mm">
                        &#8226; This Learning Voyage is a continuous and comprehensive assessment of child
                        through out the year in scholastic and co-scholastic field.
                    </fo:block>

                    <fo:block font-size="13pt" font-family="Times New Roman, serif" space-after="3mm">
                        &#8226; We at DBPS, believe in preparing students to succeed in life. Discipline, punctuality
                        &amp; smart hard work are the pillars of success attained through passion, commitment
                        &amp; dedication.
                    </fo:block>

                    <fo:block font-size="13pt"  font-family="Times New Roman, serif" space-after="3mm">
                        &#8226; This performance profile is an important document related to your ward &amp; it is
                        required to be sent back to school within two working days of its receipt.
                    </fo:block>

                    <fo:block font-size="13pt"  font-family="Times New Roman, serif" space-after="3mm">
                        &#8226; This Learning Voyage is an important document related to your ward and it is required
                        to be sent back to school with in two working days of its receipt.
                    </fo:block>

                    <fo:block font-size="13pt"  font-family="Times New Roman, serif" space-after="3mm">
                        &#8226; Scholastic areas are judged in 5 point grading system i.e A+, A, B+, B, B
                    </fo:block>


                    <!-- 7-point grading table -->
                    <fo:table border="1.5pt solid black"  width="80mm"
                              margin-left="18mm" margin-top="2mm" space-after="6mm"
                              font-size="13pt" >
                        <fo:table-column column-width="30mm"/>
                        <fo:table-column column-width="50mm"/>
                        <fo:table-column column-width="50mm"/>
                        <fo:table-header text-align="center" font-weight="bold">
                            <fo:table-row background-color="#f2f2f2">
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">Grade</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">Out of 20</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">Out of 100</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body text-align="center">
                            <fo:table-row>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">A+</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block text-align="center">16-20</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block text-align="center">90-100</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">A</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black">
                                    <fo:block text-align="center">11-15</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black">
                                    <fo:block text-align="center">70-89</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">B+</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">6-10</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">50-69</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">B</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">3-5</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">30-49</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">B-</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">&lt;2 Or Absent </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1.5pt solid black" >
                                    <fo:block text-align="center">0-29</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>

                    <fo:block font-size="13pt" font-family="Times New Roman, serif" space-after="3mm">
                        &#8226; Co-scholastic areas are assessed under 5 point grading system as above.
                    </fo:block>


                    <!-- DBPS program -->
                    <fo:block font-size="13pt"  font-family="Times New Roman, serif" space-after="12mm">
                        &#8226; At DBPS, we work at grass-root level through our Connecting The Roots Program
                        which encircles learning of Music, Dance, ABACUS, Art &amp; Craft, Drawing,
                        Meditation, Vedik Shiksha, Sports, Games, Projects, Practical experiences, Labs,
                        various digitals &amp; 5 senses oriented activities in academic &amp; co-curricular field.
                    </fo:block>
                </fo:block>

                <!-- Signatures -->
                <fo:table border="none" width="100%" space-after="8mm">
                    <fo:table-column column-width="50%"/>
                    <fo:table-column column-width="50%"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block>
                                    <fo:leader leader-pattern="dots" leader-length="100%"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-left="5mm">
                                <fo:block>
                                    <fo:leader leader-pattern="dots" leader-length="100%"/>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block text-align="center" font-size="12pt" font-weight="bold" font-family="Times New Roman, serif">
                                    Specimen Signature of Mother
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-left="5mm">
                                <fo:block text-align="center" font-size="12pt" font-weight="bold" font-family="Times New Roman, serif">
                                    Specimen Signature of Father / Guardian
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <!-- Contact number -->
                <fo:block text-align="center" font-size="15pt" font-weight="bold"
                          font-family="Times New Roman, serif" space-before="2mm" color="#11064C">
                    CONTACT NO.: 9533 000 123
                </fo:block>

            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>
